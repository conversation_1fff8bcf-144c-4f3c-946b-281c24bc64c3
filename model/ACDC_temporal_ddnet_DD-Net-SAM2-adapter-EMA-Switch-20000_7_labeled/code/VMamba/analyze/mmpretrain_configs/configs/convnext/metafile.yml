Collections:
  - Name: ConvNeXt
    Metadata:
      Training Data: ImageNet-1k
      Architecture:
        - 1x1 Convolution
        - LayerScale
    Paper:
      URL: https://arxiv.org/abs/2201.03545v1
      Title: A ConvNet for the 2020s
    README: configs/convnext/README.md
    Code:
      Version: v0.20.1
      URL: https://github.com/open-mmlab/mmpretrain/blob/v0.20.1/mmcls/models/backbones/convnext.py

Models:
  - Name: convnext-tiny_32xb128_in1k
    Metadata:
      FLOPs: 4457472768
      Parameters: 28589128
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 82.14
          Top 5 Accuracy: 96.06
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-tiny_32xb128_in1k_20221207-998cf3e9.pth
    Config: configs/convnext/convnext-tiny_32xb128_in1k.py
  - Name: convnext-tiny_32xb128-noema_in1k
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 4457472768
      Parameters: 28589128
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 81.95
          Top 5 Accuracy: 95.89
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-tiny_32xb128-noema_in1k_20221208-5d4509c7.pth
    Config: configs/convnext/convnext-tiny_32xb128_in1k.py
  - Name: convnext-tiny_in21k-pre_3rdparty_in1k
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 4457472768
      Parameters: 28589128
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 82.90
          Top 5 Accuracy: 96.62
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-tiny_in21k-pre_3rdparty_in1k_20221219-7501e534.pth
    Config: configs/convnext/convnext-tiny_32xb128_in1k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_tiny_22k_1k_224.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-tiny_in21k-pre_3rdparty_in1k-384px
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 13135236864
      Parameters: 28589128
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 84.11
          Top 5 Accuracy: 97.14
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-tiny_in21k-pre_3rdparty_in1k-384px_20221219-c1182362.pth
    Config: configs/convnext/convnext-tiny_32xb128_in1k-384px.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_tiny_22k_1k_384.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-small_32xb128_in1k
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 8687008512
      Parameters: 50223688
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 83.16
          Top 5 Accuracy: 96.56
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-small_32xb128_in1k_20221207-4ab7052c.pth
    Config: configs/convnext/convnext-small_32xb128_in1k.py
  - Name: convnext-small_32xb128-noema_in1k
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 8687008512
      Parameters: 50223688
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 83.21
          Top 5 Accuracy: 96.48
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-small_32xb128-noema_in1k_20221208-4a618995.pth
    Config: configs/convnext/convnext-small_32xb128_in1k.py
  - Name: convnext-small_in21k-pre_3rdparty_in1k
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 8687008512
      Parameters: 50223688
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 84.59
          Top 5 Accuracy: 97.41
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-small_in21k-pre_3rdparty_in1k_20221219-aeca4c93.pth
    Config: configs/convnext/convnext-small_32xb128_in1k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_small_22k_1k_224.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-small_in21k-pre_3rdparty_in1k-384px
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 25580818176
      Parameters: 50223688
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 85.75
          Top 5 Accuracy: 97.88
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-small_in21k-pre_3rdparty_in1k-384px_20221219-96f0bb87.pth
    Config: configs/convnext/convnext-small_32xb128_in1k-384px.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_small_22k_1k_384.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-base_32xb128_in1k
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 15359124480
      Parameters: 88591464
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 83.66
          Top 5 Accuracy: 96.74
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-base_32xb128_in1k_20221207-fbdb5eb9.pth
    Config: configs/convnext/convnext-base_32xb128_in1k.py
  - Name: convnext-base_32xb128-noema_in1k
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 15359124480
      Parameters: 88591464
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 83.64
          Top 5 Accuracy: 96.61
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-base_32xb128-noema_in1k_20221208-f8182678.pth
    Config: configs/convnext/convnext-base_32xb128_in1k.py
  - Name: convnext-base_3rdparty_in1k
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 15359124480
      Parameters: 88591464
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 83.85
          Top 5 Accuracy: 96.74
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-base_3rdparty_32xb128_in1k_20220124-d0915162.pth
    Config: configs/convnext/convnext-base_32xb128_in1k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_base_1k_224_ema.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-base_3rdparty-noema_in1k
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 15359124480
      Parameters: 88591464
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 83.71
          Top 5 Accuracy: 96.60
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-base_3rdparty_32xb128-noema_in1k_20220222-dba4f95f.pth
    Config: configs/convnext/convnext-base_32xb128_in1k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_base_1k_224.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-base_3rdparty_in1k-384px
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 45205885952
      Parameters: 88591464
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 85.10
          Top 5 Accuracy: 97.34
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-base_3rdparty_in1k-384px_20221219-c8f1dc2b.pth
    Config: configs/convnext/convnext-base_32xb128_in1k-384px.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_base_1k_384.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-base_3rdparty_in21k
    Metadata:
      Training Data: ImageNet-21k
      FLOPs: 15359124480
      Parameters: 88591464
    In Collection: ConvNeXt
    Results: null
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-base_3rdparty_in21k_20220124-13b83eec.pth
    Config: configs/convnext/convnext-base_32xb128_in21k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_base_22k_224.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-base_in21k-pre_3rdparty_in1k
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 15359124480
      Parameters: 88591464
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 85.81
          Top 5 Accuracy: 97.86
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-base_in21k-pre-3rdparty_32xb128_in1k_20220124-eb2d6ada.pth
    Config: configs/convnext/convnext-base_32xb128_in1k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_base_22k_1k_224.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-base_in21k-pre-3rdparty_in1k-384px
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 45205885952
      Parameters: 88591464
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 86.82
          Top 5 Accuracy: 98.25
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-base_in21k-pre-3rdparty_in1k-384px_20221219-4570f792.pth
    Config: configs/convnext/convnext-base_32xb128_in1k-384px.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_base_22k_1k_384.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-large_3rdparty_in1k
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 34368026112
      Parameters: 197767336
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 84.30
          Top 5 Accuracy: 96.89
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-large_3rdparty_64xb64_in1k_20220124-f8a0ded0.pth
    Config: configs/convnext/convnext-large_64xb64_in1k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_large_1k_224_ema.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-large_3rdparty_in1k-384px
    Metadata:
      Training Data: ImageNet-1k
      FLOPs: 101103214080
      Parameters: 197767336
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 85.50
          Top 5 Accuracy: 97.59
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-large_3rdparty_in1k-384px_20221219-6dd29d10.pth
    Config: configs/convnext/convnext-large_64xb64_in1k-384px.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_large_1k_384.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-large_3rdparty_in21k
    Metadata:
      Training Data: ImageNet-21k
      FLOPs: 34368026112
      Parameters: 197767336
    In Collection: ConvNeXt
    Results: null
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-large_3rdparty_in21k_20220124-41b5a79f.pth
    Config: configs/convnext/convnext-large_64xb64_in21k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_large_22k_224.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-large_in21k-pre_3rdparty_in1k
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 34368026112
      Parameters: 197767336
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 86.61
          Top 5 Accuracy: 98.04
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-large_in21k-pre-3rdparty_64xb64_in1k_20220124-2412403d.pth
    Config: configs/convnext/convnext-large_64xb64_in1k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_large_22k_1k_224.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-large_in21k-pre-3rdparty_in1k-384px
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 101103214080
      Parameters: 197767336
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 87.46
          Top 5 Accuracy: 98.37
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-large_in21k-pre-3rdparty_in1k-384px_20221219-6d38dd66.pth
    Config: configs/convnext/convnext-large_64xb64_in1k-384px.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_large_22k_1k_384.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-xlarge_3rdparty_in21k
    Metadata:
      Training Data: ImageNet-21k
      FLOPs: 60929820672
      Parameters: 350196968
    In Collection: ConvNeXt
    Results: null
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-xlarge_3rdparty_in21k_20220124-f909bad7.pth
    Config: configs/convnext/convnext-xlarge_64xb64_in21k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_xlarge_22k_224.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-xlarge_in21k-pre_3rdparty_in1k
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 60929820672
      Parameters: 350196968
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 86.97
          Top 5 Accuracy: 98.20
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-xlarge_in21k-pre-3rdparty_64xb64_in1k_20220124-76b6863d.pth
    Config: configs/convnext/convnext-xlarge_64xb64_in1k.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_xlarge_22k_1k_224_ema.pth
      Code: https://github.com/facebookresearch/ConvNeXt
  - Name: convnext-xlarge_in21k-pre-3rdparty_in1k-384px
    Metadata:
      Training Data:
        - ImageNet-21k
        - ImageNet-1k
      FLOPs: 179196798976
      Parameters: 350196968
    In Collection: ConvNeXt
    Results:
      - Dataset: ImageNet-1k
        Metrics:
          Top 1 Accuracy: 87.76
          Top 5 Accuracy: 98.55
        Task: Image Classification
    Weights: https://download.openmmlab.com/mmclassification/v0/convnext/convnext-xlarge_in21k-pre-3rdparty_in1k-384px_20221219-b161bc14.pth
    Config: configs/convnext/convnext-xlarge_64xb64_in1k-384px.py
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/convnext/convnext_xlarge_22k_1k_384_ema.pth
      Code: https://github.com/facebookresearch/ConvNeXt
